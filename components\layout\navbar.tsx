"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Phone, MessageCircle } from "lucide-react"
import { Home, Building2, Calculator, Key, FileText, TrendingUp, Handshake, Scale, PieChart } from "lucide-react"
import { usePreloader } from "@/components/providers/preloader-provider"

export default function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname()
  const { isLoading } = usePreloader()
  
  // Check if current page should have white navbar text initially
  const isContactPage = pathname === '/contact'
  const isCareerPage = pathname === '/career'
  const isCalculatorPage = pathname === '/calculator'
  const isInsightsPage = pathname === '/insights' || pathname.startsWith('/insights/')
  const isAboutPage = pathname === '/about'
  const isServicesPage = pathname === '/services' || pathname.startsWith('/services/')
  const isProjectsPage = pathname === '/projects' // Only projects listing page, not detail pages
  const isProjectDetailPage = pathname.startsWith('/projects/') && pathname !== '/projects'
  const isDemoPage = pathname === '/demo' || pathname.startsWith('/demo/')
  const isHomePage = pathname === '/'

  // All pages that should have white navbar text initially (before scroll)
  const shouldHaveWhiteText = isContactPage || isCareerPage || isCalculatorPage || isInsightsPage || isAboutPage || isServicesPage || isProjectsPage || isProjectDetailPage || isDemoPage || isHomePage

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Dynamic text colors based on page and scroll state
  const getTextColor = () => {
    if (shouldHaveWhiteText && !isScrolled) {
      return "text-white hover:text-white"
    }
    return "text-gray-900 hover:text-burgundy-600"
  }

  // Dynamic hover background based on page and scroll state
  const getHoverBg = () => {
    // Remove all hover backgrounds, only keep underline
    return ""
  }

  // Dynamic mobile hamburger colors
  const getHamburgerColor = () => {
    if (shouldHaveWhiteText && !isScrolled) {
      return "bg-white"
    }
    return "bg-gray-600"
  }

  // Get active state classes for navigation links (only text color, no background)
  const getActiveClasses = (path: string) => {
    const isActive = pathname === path
    if (isActive) {
      if (shouldHaveWhiteText && !isScrolled) {
        return "text-white"
      }
      return "text-burgundy-700"
    }
    return ""
  }

  // Get active underline classes
  const getActiveUnderline = (path: string) => {
    const isActive = pathname === path
    if (isActive) {
      return "w-3/4"
    }
    return "w-0 group-hover:w-3/4"
  }

  // Get mobile active classes
  const getMobileActiveClasses = (path: string) => {
    const isActive = pathname === path
    if (isActive) {
      return "bg-burgundy-100 text-burgundy-700"
    }
    return "text-gray-900 hover:text-burgundy-600 hover:bg-burgundy-50"
  }

  const servicesItems = [
    { title: "Buy", href: "/services/buy", description: "Find your dream property" },
    { title: "Rent", href: "/services/rent", description: "Rental properties" },
    { title: "Lease", href: "/services/lease", description: "Commercial leasing" },
    { title: "Pre-lease", href: "/services/pre-lease", description: "Pre-launch properties" },
    { title: "Sell", href: "/services/sell", description: "Sell your property" },
    { title: "Invest", href: "/services/invest", description: "Investment opportunities" },
  ]

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "bg-white backdrop-blur-md shadow-lg border-b border-gray-100" : "bg-transparent"
      } ${isLoading ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <Image
              src="/images/logo.png"
              alt="Dwelling Desire"
              width={180}
              height={60}
              className={`h-20 w-auto transition-all duration-300 group-hover:scale-105 ${
                shouldHaveWhiteText && !isScrolled ? 'brightness-0 invert' : ''
              }`}
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center">
            <nav className="flex items-center space-x-1">
              {/* Home */}
              <Link
                href="/"
                prefetch={true}
                className={`px-4 py-2 ${getTextColor()} ${getActiveClasses('/')} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Home
                <span className={`absolute bottom-0 left-1/2 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 ${getActiveUnderline('/')} transform -translate-x-1/2`}></span>
              </Link>

              {/* About Us */}
              <Link
                href="/about"
                prefetch={true}
                className={`px-4 py-2 ${getTextColor()} ${getActiveClasses('/about')} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                About Us
                <span className={`absolute bottom-0 left-1/2 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 ${getActiveUnderline('/about')} transform -translate-x-1/2`}></span>
              </Link>

              {/* Services Dropdown */}
              <div className="relative group">
                <button className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} flex items-center space-x-1 relative`}>
                  <span>Services</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-300 group-hover:rotate-180"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  <span className={`absolute bottom-0 left-1/2 w-0 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2`}></span>
                </button>

                {/* Services Dropdown Menu */}
                <div className="absolute top-full left-0 mt-2 w-96 bg-white rounded-2xl shadow-2xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-[60] backdrop-blur-sm">
                  <div className="p-3">
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        { title: "Buy", desc: "Find your dream property", icon: Home, href: "/services#residential" },
                        { title: "Rent", desc: "Rental properties", icon: Key, href: "/services#residential" },
                        { title: "Lease", desc: "Commercial leasing", icon: Building2, href: "/services#commercial" },
                        {
                          title: "Pre-lease",
                          desc: "Pre-launch properties",
                          icon: FileText,
                          href: "/services#project-marketing",
                        },
                        { title: "Sell", desc: "Sell your property", icon: Handshake, href: "/services#residential" },
                        {
                          title: "Invest",
                          desc: "Investment opportunities",
                          icon: TrendingUp,
                          href: "/services#commercial",
                        },
                      ].map((item) => {
                        const IconComponent = item.icon
                        return (
                          <Link
                            key={item.title}
                            href={item.href}
                            prefetch={true}
                            className="flex items-start space-x-3 p-4 rounded-xl hover:bg-burgundy-50 transition-all duration-300 group/item"
                          >
                            <div className="w-10 h-10 bg-burgundy-100 rounded-full flex items-center justify-center group-hover/item:bg-burgundy-200 transition-colors flex-shrink-0">
                              <IconComponent className="w-4 h-4 text-burgundy-600" />
                            </div>
                            <div>
                              <div className="font-semibold text-gray-900 group-hover/item:text-burgundy-600 transition-colors">
                                {item.title}
                              </div>
                              <div className="text-xs text-gray-500 mt-0.5">{item.desc}</div>
                            </div>
                          </Link>
                        )
                      })}
                    </div>

                    {/* View All Services Link */}
                    <div className="border-t border-gray-100 mt-3 pt-3">
                      <Link
                        href="/services"
                        className="flex items-center justify-center space-x-2 p-3 rounded-xl bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white hover:from-burgundy-700 hover:to-burgundy-800 transition-all duration-300 group/all"
                      >
                        <span className="font-semibold text-sm">View All Services</span>
                        <svg className="w-4 h-4 group-hover/all:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              {/* Projects */}
              <Link
                href="/projects"
                className={`px-4 py-2 ${getTextColor()} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} ${getActiveClasses('/projects')} relative group`}
              >
                Projects
                <span className={`absolute bottom-0 left-1/2 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 transform -translate-x-1/2 ${getActiveUnderline('/projects')}`}></span>
              </Link>

              {/* Insights */}
              <Link
                href="/insights"
                className={`px-4 py-2 ${getTextColor()} ${getActiveClasses('/insights')} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Insights
                <span className={`absolute bottom-0 left-1/2 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 ${getActiveUnderline('/insights')} transform -translate-x-1/2`}></span>
              </Link>

              {/* Calculator */}
              <Link
                href="/calculator"
                className={`px-4 py-2 ${getTextColor()} ${getActiveClasses('/calculator')} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Calculator
                <span className={`absolute bottom-0 left-1/2 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 ${getActiveUnderline('/calculator')} transform -translate-x-1/2`}></span>
              </Link>

              {/* Career */}
              <Link
                href="/career"
                className={`px-4 py-2 ${getTextColor()} ${getActiveClasses('/career')} font-medium transition-all duration-300 rounded-lg ${getHoverBg()} relative group`}
              >
                Career
                <span className={`absolute bottom-0 left-1/2 h-0.5 ${shouldHaveWhiteText && !isScrolled ? 'bg-white' : 'bg-burgundy-600'} transition-all duration-300 ${getActiveUnderline('/career')} transform -translate-x-1/2`}></span>
              </Link>
            </nav>

            {/* CTA Button */}
            <div className={`ml-8 transition-opacity duration-300 ${isLoading ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
              <Button
                asChild
                className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 hover:from-burgundy-700 hover:to-burgundy-800 text-white px-6 py-2.5 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <Link href="/contact" className="flex items-center space-x-2">
                  <span>Let's Talk</span>
                  <MessageCircle size={16} />
                </Link>
              </Button>
            </div>
          </div>

          {/* Mobile Menu Button */}
                      <button
              className={`lg:hidden p-2 rounded-lg transition-colors duration-300 ${
                shouldHaveWhiteText && !isScrolled ? 'hover:bg-white/20' : 'hover:bg-gray-100'
              }`}
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <div className="relative w-6 h-6">
              <span
                className={`absolute block w-6 h-0.5 ${getHamburgerColor()} transition-all duration-300 ${isMobileMenuOpen ? "rotate-45 top-3" : "top-1"}`}
              ></span>
              <span
                className={`absolute block w-6 h-0.5 ${getHamburgerColor()} transition-all duration-300 top-3 ${isMobileMenuOpen ? "opacity-0" : "opacity-100"}`}
              ></span>
              <span
                className={`absolute block w-6 h-0.5 ${getHamburgerColor()} transition-all duration-300 ${isMobileMenuOpen ? "-rotate-45 top-3" : "top-5"}`}
              ></span>
            </div>
          </button>
        </div>

        {/* Enhanced Mobile Menu */}
        <div
          className={`lg:hidden transition-all duration-300 overflow-hidden ${isMobileMenuOpen ? "max-h-[80vh] opacity-100" : "max-h-0 opacity-0"}`}
        >
          <div className="bg-white/95 backdrop-blur-md border-t border-gray-100 py-4 max-h-[80vh] overflow-y-auto">
            <nav className="flex flex-col space-y-2">
              <Link
                href="/"
                prefetch={true}
                className={`px-4 py-3 ${getMobileActiveClasses('/')} font-medium transition-all duration-300 rounded-lg mx-2`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Home
              </Link>

              <Link
                href="/about"
                prefetch={true}
                className={`px-4 py-3 ${getMobileActiveClasses('/about')} font-medium transition-all duration-300 rounded-lg mx-2`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                About Us
              </Link>

              {/* Mobile Services - Collapsible */}
              <div className="mx-2">
                <details className="group">
                  <summary className="px-4 py-3 text-gray-700 hover:text-burgundy-600 hover:bg-burgundy-50 font-medium transition-all duration-300 rounded-lg cursor-pointer flex items-center justify-between">
                    <span>Services</span>
                    <svg
                      className="w-4 h-4 transition-transform duration-300 group-open:rotate-180"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </summary>
                  <div className="mt-2 ml-4 space-y-1">
                    {[
                      { title: "Buy", href: "/services#residential", icon: Home },
                      { title: "Rent", href: "/services#residential", icon: Key },
                      { title: "Lease", href: "/services#commercial", icon: Building2 },
                      { title: "Pre-lease", href: "/services#project-marketing", icon: FileText },
                      { title: "Sell", href: "/services#residential", icon: Handshake },
                      { title: "Invest", href: "/services#commercial", icon: TrendingUp },
                    ].map((item) => {
                      const IconComponent = item.icon
                      return (
                        <Link
                          key={item.title}
                          href={item.href}
                          className="flex items-center space-x-3 px-3 py-2 text-sm text-gray-600 hover:text-burgundy-600 hover:bg-burgundy-50 rounded-lg transition-all duration-300"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <div className="w-8 h-8 bg-burgundy-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <IconComponent size={16} className="text-burgundy-600" />
                          </div>
                          <span>{item.title}</span>
                        </Link>
                      )
                    })}

                    {/* View All Services Link for Mobile */}
                    <Link
                      href="/services"
                      className="flex items-center justify-center space-x-2 px-3 py-2 mt-2 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white hover:from-burgundy-700 hover:to-burgundy-800 rounded-lg transition-all duration-300"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <span className="text-sm font-semibold">View All Services</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </details>
              </div>

              <Link
                href="/projects"
                className={`px-4 py-3 ${getMobileActiveClasses('/projects')} font-medium transition-all duration-300 rounded-lg mx-2`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Projects
              </Link>

              <Link
                href="/insights"
                className={`px-4 py-3 ${getMobileActiveClasses('/insights')} font-medium transition-all duration-300 rounded-lg mx-2`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Insights
              </Link>

              <Link
                href="/calculator"
                className={`px-4 py-3 ${getMobileActiveClasses('/calculator')} font-medium transition-all duration-300 rounded-lg mx-2`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Calculator
              </Link>

              <Link
                href="/career"
                className={`px-4 py-3 ${getMobileActiveClasses('/career')} font-medium transition-all duration-300 rounded-lg mx-2`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Career
              </Link>

              <div className={`px-4 pt-4 transition-opacity duration-300 ${isLoading ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
                <Button
                  asChild
                  className="w-full bg-gradient-to-r from-burgundy-600 to-burgundy-700 hover:from-burgundy-700 hover:to-burgundy-800 text-white rounded-full font-semibold"
                >
                  <Link href="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                    Let's Talk
                  </Link>
                </Button>
              </div>
            </nav>
          </div>
        </div>
      </div>

    </nav>
  )
}
